
Here are a few projects that are built on fastreid.
They are examples of how to use fastrei as a library, to make your projects more maintainable.

# Projects by JDAI

Note that these are research projects, and therefore may not have the same level of support or stability of fastreid.

- [Deep Spatial Feature Reconstruction for Partial Person Re-identification](https://github.com/JDAI-CV/fast-reid/tree/master/projects/PartialReID)
- [Black Re-ID: A Head-shoulder Descriptor for the Challenging Problem of Person Re-Identification](https://github.com/JDAI-CV/fast-reid/tree/master/projects/HAA)
- [Image Classification](https://github.com/JDAI-CV/fast-reid/tree/master/projects/FastCls)
- [Face Recognition](https://github.com/JDAI-CV/fast-reid/tree/master/projects/FastFace)
- [Image Retrieval](https://github.com/JDAI-CV/fast-reid/tree/master/projects/FastRetri)
- [Attribute Recognition](https://github.com/JDAI-CV/fast-reid/tree/master/projects/FastAttr)
- [Hyper-Parameters Optimization](https://github.com/JDAI-CV/fast-reid/tree/master/projects/FastTune)
- [Overhaul Distillation](https://github.com/JDAI-CV/fast-reid/tree/master/projects/FastDistill)
- Semi-Supervised Domain Generalizable Person Re-Identification. [code](https://github.com/xiaomingzhid/sskd) and [paper](https://arxiv.org/pdf/2108.05045.pdf)

# External Projects

External projects in the community that use fastreid:

- [FastReID of Interpreter Project (ICCV 2021)](https://github.com/SheldongChen/AMD.github.io)

# Competitions

- NAIC20 reid track [1-st solution](https://github.com/JDAI-CV/fast-reid/tree/master/projects/NAIC20) 
