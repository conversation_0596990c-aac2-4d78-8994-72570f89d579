MODEL:
  META_ARCHITECTURE: Baseline

  BACKBONE:
    NAME: build_resnet_backbone
    DEPTH: 50x
    NORM: FrozenBN
    LAST_STRIDE: 1
    FEAT_DIM: 2048
    PRETRAIN: True

  HEADS:
    NAME: EmbeddingHead
    NORM: syncBN
    WITH_BNNECK: True
    NECK_FEAT: after
    EMBEDDING_DIM: 0
    POOL_LAYER: GeneralizedMeanPooling
    CLS_LAYER: Linear

  LOSSES:
    NAME: ("CrossEntropyLoss",)

    CE:
      EPSILON: 0.1
      SCALE: 1.

INPUT:
  SIZE_TRAIN: [256, 256]
  SIZE_TEST: [256, 256]

  CROP:
    ENABLED: True
    SIZE: [224,]
    SCALE: [0.16, 1.]
    RATIO: [0.75, 1.33333]

  FLIP:
    ENABLED: True

  CJ:
    ENABLED: False
    BRIGHTNESS: 0.3
    CONTRAST: 0.3
    SATURATION: 0.1
    HUE: 0.1


DATALOADER:
  SAMPLER_TRAIN: TrainingSampler
  NUM_WORKERS: 8

SOLVER:
  MAX_EPOCH: 100
  AMP:
    ENABLED: True

  OPT: SGD
  SCHED: CosineAnnealingLR

  BASE_LR: 0.003
  MOMENTUM: 0.99
  NESTEROV: True

  BIAS_LR_FACTOR: 1.
  WEIGHT_DECAY: 0.0005
  WEIGHT_DECAY_BIAS: 0.
  IMS_PER_BATCH: 128

  ETA_MIN_LR: 0.00003

  WARMUP_FACTOR: 0.1
  WARMUP_ITERS: 1000

  CHECKPOINT_PERIOD: 10

  CLIP_GRADIENTS:
    ENABLED: True

TEST:
  EVAL_PERIOD: 10
  IMS_PER_BATCH: 256

CUDNN_BENCHMARK: True