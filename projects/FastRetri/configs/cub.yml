_BASE_: base-image_retri.yml

MODEL:
  LOSSES:
    CE:
      EPSILON: 0.3

INPUT:
  SIZE_TRAIN: [256,]
  SIZE_TEST: [256,]

  CJ:
    ENABLED: True
    BRIGHTNESS: 0.25
    CONTRAST: 0.25
    SATURATION: 0.25
    HUE: 0.0

SOLVER:
  MAX_EPOCH: 30

  BASE_LR: 0.02
  ETA_MIN_LR: 0.00002

  NESTEROV: False
  MOMENTUM: 0.

TEST:
  RECALLS: [ 1, 2, 4, 8, 16, 32 ]

DATASETS:
  NAMES: ("CUB",)
  TESTS: ("CUB",)

OUTPUT_DIR: projects/FastRetri/logs/r50-base_cub