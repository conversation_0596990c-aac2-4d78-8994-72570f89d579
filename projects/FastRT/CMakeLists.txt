cmake_minimum_required(VERSION 2.6)

set(LIB<PERSON>ARY_NAME "FastRT" CACHE STRING "The Fastreid-tensorrt library name")

set(LIBARARY_VERSION_MAJOR "0")
set(LIBARARY_VERSION_MINOR "0")
set(LIB<PERSON>ARY_VERSION_SINOR "5")
set(LIBARARY_SOVERSION "0")
set(LIBARARY_VERSION "${LIBARARY_VERSION_MAJOR}.${LIBARARY_VERSION_MINOR}.${LIBARARY_VERSION_SINOR}")
project(${LIBARARY_NAME}${LIBARARY_VERSION})

add_definitions(-std=c++11)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread -Wall -Ofast -Wfatal-errors -D_MWAITXINTRIN_H_INCLUDED")
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${CMAKE_SOURCE_DIR}/")
set(CMAKE_BUILD_TYPE Release)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_LINK_EXECUTABLE ${CMAKE_CXX_LINK_EXECUTABLE})

# option for shared or static
set(TARGET "SHARED" CACHE STRING "SHARED or STATIC" FORCE)

if("${TARGET}" STREQUAL "SHARED")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")
message("Build Engine as shared library")
else()
  message("Build Engine as static library")
endif()

option(CUDA_USE_STATIC_CUDA_RUNTIME "Use Static CUDA"     OFF)
option(BUILD_FASTRT_ENGINE     "Build FastRT Engine"       ON)
option(BUILD_DEMO              "Build DEMO"                ON)
option(BUILD_FP16              "Build Engine as FP16"     OFF)
option(BUILD_INT8              "Build Engine as INT8"     OFF)
option(USE_CNUMPY              "Include CNPY libs"        OFF)
option(BUILD_PYTHON_INTERFACE  "Build Python Interface"   OFF)

set(SOLUTION_DIR ${CMAKE_CURRENT_SOURCE_DIR})
message("CMAKE_CURRENT_SOURCE_DIR: " ${SOLUTION_DIR})

if(USE_CNUMPY)
  add_definitions(-DUSE_CNUMPY)
endif()

if(BUILD_INT8)
  add_definitions(-DBUILD_INT8)
  message("Build Engine as INT8")
  set(INT8_CALIBRATE_DATASET_PATH "/data/Market-1501-v15.09.15/bounding_box_test/" CACHE STRING "Path to calibrate dataset(end with /)")
  message("INT8_CALIBRATE_DATASET_PATH: " ${INT8_CALIBRATE_DATASET_PATH})
  configure_file(${SOLUTION_DIR}/include/fastrt/config.h.in ${SOLUTION_DIR}/include/fastrt/config.h @ONLY)
elseif(BUILD_FP16)
  add_definitions(-DBUILD_FP16)
  message("Build Engine as FP16")
else()
  message("Build Engine as FP32")
endif()

if(BUILD_FASTRT_ENGINE)
  add_subdirectory(fastrt)
  message(STATUS "BUILD_FASTREID_ENGINE: ON")
else()
  message(STATUS "BUILD_FASTREID_ENGINE: OFF")
endif()

if(BUILD_DEMO)
  add_subdirectory(demo)
  message(STATUS "BUILD_DEMO: ON")
else()
  message(STATUS "BUILD_DEMO: OFF")
endif()

if(BUILD_PYTHON_INTERFACE)
  add_subdirectory(pybind_interface)
  message(STATUS "BUILD_PYTHON_INTERFACE: ON")
else()
  message(STATUS "BUILD_PYTHON_INTERFACE: OFF")
endif()