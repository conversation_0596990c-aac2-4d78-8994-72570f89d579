_BASE_: ../Base-bagtricks.yml

INPUT:
  SIZE_TRAIN: [256, 256]
  SIZE_TEST: [256, 256]

MODEL:
  BACKBONE:
    WITH_IBN: True

  HEADS:
    POOL_LAYER: GeneralizedMeanPooling

  LOSSES:
    TRI:
      HARD_MINING: False
      MARGIN: 0.0

DATASETS:
  NAMES: ("VeRiWild",)
  TESTS: ("SmallVeRiWild", "MediumVeRiWild", "LargeVeRiWild",)

SOLVER:
  IMS_PER_BATCH: 512 # 512 For 4 GPUs
  MAX_EPOCH: 120
  STEPS: [30, 70, 90]
  WARMUP_ITERS: 5000

  CHECKPOINT_PERIOD: 20

TEST:
  EVAL_PERIOD: 10
  IMS_PER_BATCH: 128

OUTPUT_DIR: logs/veriwild/bagtricks_R50-ibn_4gpu
