_BASE_: ../Base-bagtricks.yml

INPUT:
  SIZE_TRAIN: [256, 256]
  SIZE_TEST: [256, 256]

MODEL:
  BACKBONE:
    WITH_IBN: True
  HEADS:
    POOL_LAYER: GeneralizedMeanPooling

  LOSSES:
    TRI:
      HARD_MINING: False
      MARGIN: 0.0

DATASETS:
  NAMES: ("VehicleID",)
  TESTS: ("SmallVehicleID", "MediumVehicleID", "LargeVehicleID",)

SOLVER:
  BIAS_LR_FACTOR: 1.

  IMS_PER_BATCH: 512
  MAX_EPOCH: 60
  STEPS: [30, 50]
  WARMUP_ITERS: 2000

  CHECKPOINT_PERIOD: 20

TEST:
  EVAL_PERIOD: 20
  IMS_PER_BATCH: 128

OUTPUT_DIR: logs/vehicleid/bagtricks_R50-ibn_4gpu
