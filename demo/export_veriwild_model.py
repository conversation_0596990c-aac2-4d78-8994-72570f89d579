# encoding: utf-8
"""
VERIWild模型导出工具
将PyTorch模型导出为可部署的格式
"""

import os
import sys
import torch
import argparse
from pathlib import Path

sys.path.append('.')

from fastreid.config import get_cfg
from fastreid.utils.logger import setup_logger
from fastreid.modeling import build_model
from fastreid.utils.checkpoint import Checkpointer

# 设置日志
setup_logger(name="fastreid")

def export_pytorch_model(config_file, model_weights, output_dir="outputs/exported_model", model_name="veriwild_vehicle_reid"):
    """
    导出PyTorch模型为TorchScript格式
    
    Args:
        config_file: 配置文件路径
        model_weights: 模型权重文件路径
        output_dir: 输出目录
        model_name: 模型名称
    """
    print(f"=== 导出VERIWild模型 ===")
    print(f"配置文件: {config_file}")
    print(f"模型权重: {model_weights}")
    
    # 检查输入文件
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return None
        
    if not os.path.exists(model_weights):
        print(f"❌ 模型权重不存在: {model_weights}")
        return None
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 加载配置
        cfg = get_cfg()
        cfg.merge_from_file(config_file)
        cfg.MODEL.WEIGHTS = model_weights
        cfg.MODEL.DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
        cfg.freeze()
        
        print(f"✅ 配置加载成功")
        print(f"   设备: {cfg.MODEL.DEVICE}")
        print(f"   输入尺寸: {cfg.INPUT.SIZE_TEST}")
        print(f"   骨干网络: {cfg.MODEL.BACKBONE.NAME}")
        
        # 构建模型
        model = build_model(cfg)
        model.eval()
        
        # 加载权重
        checkpointer = Checkpointer(model)
        checkpointer.load(cfg.MODEL.WEIGHTS)
        
        print(f"✅ 模型加载成功")
        
        # 创建示例输入
        batch_size = 1
        channels = 3
        height, width = cfg.INPUT.SIZE_TEST
        
        dummy_input = torch.randn(batch_size, channels, height, width)
        if cfg.MODEL.DEVICE == "cuda":
            model = model.cuda()
            dummy_input = dummy_input.cuda()
        
        print(f"✅ 示例输入创建: {dummy_input.shape}")
        
        # 导出TorchScript模型
        with torch.no_grad():
            # 先进行一次前向传播确保模型正常工作
            output = model(dummy_input)
            print(f"✅ 模型前向传播成功，输出形状: {output.shape}")
            
            # 使用torch.jit.trace导出
            traced_model = torch.jit.trace(model, dummy_input)
            
            # 保存traced模型
            traced_path = os.path.join(output_dir, f"{model_name}_traced.pt")
            traced_model.save(traced_path)
            print(f"✅ TorchScript traced模型保存: {traced_path}")
            
            # 尝试使用torch.jit.script导出（可能失败，但值得尝试）
            try:
                scripted_model = torch.jit.script(model)
                scripted_path = os.path.join(output_dir, f"{model_name}_scripted.pt")
                scripted_model.save(scripted_path)
                print(f"✅ TorchScript scripted模型保存: {scripted_path}")
            except Exception as e:
                print(f"⚠️  TorchScript script导出失败: {e}")
                print("   这是正常的，traced模型已足够使用")
        
        # 保存模型信息
        model_info = {
            "model_name": model_name,
            "config_file": config_file,
            "model_weights": model_weights,
            "input_shape": [batch_size, channels, height, width],
            "output_shape": list(output.shape),
            "device": cfg.MODEL.DEVICE,
            "backbone": cfg.MODEL.BACKBONE.NAME,
            "heads": cfg.MODEL.HEADS.NAME,
            "feature_dim": cfg.MODEL.BACKBONE.FEAT_DIM
        }
        
        import json
        info_path = os.path.join(output_dir, f"{model_name}_info.json")
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 模型信息保存: {info_path}")
        
        # 测试导出的模型
        print(f"\n🔍 测试导出的模型...")
        loaded_model = torch.jit.load(traced_path)
        loaded_model.eval()
        
        with torch.no_grad():
            test_output = loaded_model(dummy_input)
            
            # 比较输出
            diff = torch.abs(output - test_output).max().item()
            print(f"✅ 模型测试成功")
            print(f"   原始输出 vs 导出输出最大差异: {diff:.8f}")
            
            if diff < 1e-5:
                print(f"✅ 导出模型与原始模型输出一致")
            else:
                print(f"⚠️  导出模型与原始模型输出存在差异")
        
        print(f"\n🎉 模型导出完成!")
        print(f"输出目录: {output_dir}")
        print(f"主要文件:")
        print(f"  - {model_name}_traced.pt: TorchScript traced模型")
        print(f"  - {model_name}_info.json: 模型信息")
        
        return traced_path
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_exported_model(model_path, model_info_path):
    """测试导出的模型"""
    print(f"\n=== 测试导出的模型 ===")
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(model_info_path):
        print(f"❌ 模型信息文件不存在: {model_info_path}")
        return
    
    try:
        # 加载模型信息
        import json
        with open(model_info_path, 'r', encoding='utf-8') as f:
            model_info = json.load(f)
        
        print(f"模型信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
        # 加载模型
        model = torch.jit.load(model_path)
        model.eval()
        
        # 创建测试输入
        input_shape = model_info["input_shape"]
        test_input = torch.randn(*input_shape)
        
        # 测试推理时间
        import time
        
        # 预热
        with torch.no_grad():
            _ = model(test_input)
        
        # 测试推理时间
        num_runs = 10
        times = []
        
        for _ in range(num_runs):
            start_time = time.time()
            with torch.no_grad():
                output = model(test_input)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\n📊 性能测试结果:")
        print(f"  平均推理时间: {avg_time:.2f}ms")
        print(f"  最短时间: {min_time:.2f}ms")
        print(f"  最长时间: {max_time:.2f}ms")
        print(f"  输出形状: {output.shape}")
        print(f"  每秒处理: {1000/avg_time:.1f} 张图片")
        
        print(f"✅ 导出模型测试成功!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="VERIWild模型导出工具")
    
    parser.add_argument(
        "--config-file",
        default="/home/<USER>/project/fast-reid/logs/veriwild/bagtricks_R50-ibn_4gpu/config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--model-weights",
        default="/home/<USER>/project/fast-reid/veriwild_bot_R50-ibn.pth",
        help="模型权重文件路径"
    )
    
    parser.add_argument(
        "--output-dir",
        default="outputs/exported_model",
        help="输出目录"
    )
    
    parser.add_argument(
        "--model-name",
        default="veriwild_vehicle_reid",
        help="模型名称"
    )
    
    parser.add_argument(
        "--test-only",
        action="store_true",
        help="仅测试已导出的模型"
    )
    
    args = parser.parse_args()
    
    print("🚀 VERIWild模型导出工具")
    print("=" * 50)
    
    if args.test_only:
        # 仅测试模式
        model_path = os.path.join(args.output_dir, f"{args.model_name}_traced.pt")
        info_path = os.path.join(args.output_dir, f"{args.model_name}_info.json")
        test_exported_model(model_path, info_path)
    else:
        # 导出模式
        exported_path = export_pytorch_model(
            args.config_file,
            args.model_weights,
            args.output_dir,
            args.model_name
        )
        
        if exported_path:
            # 自动测试导出的模型
            info_path = os.path.join(args.output_dir, f"{args.model_name}_info.json")
            test_exported_model(exported_path, info_path)

if __name__ == "__main__":
    main()
