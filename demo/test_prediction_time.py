# encoding: utf-8
"""
车辆识别工具预测时间性能测试
"""

import os
import sys
import time
import statistics
import cv2
import numpy as np
from pathlib import Path

sys.path.append('.')

from vehicle_recognition_tool import VehicleRecognitionTool

def test_feature_extraction_time():
    """测试特征提取时间"""
    print("=== 特征提取时间测试 ===")
    
    # 初始化工具
    tool = VehicleRecognitionTool(
        config_file="configs/VERIWild/bagtricks_R50-ibn.yml",
        model_weights="veriwild_bot_R50-ibn.pth",
        similarity_threshold=0.6
    )
    
    # 准备测试图片
    test_images = []
    query_dir = "datasets/veri/image_query"
    
    if os.path.exists(query_dir):
        image_files = [f for f in os.listdir(query_dir) if f.endswith('.jpg')][:10]
        test_images = [os.path.join(query_dir, f) for f in image_files]
    
    if not test_images:
        print("❌ 没有找到测试图片")
        return
    
    print(f"📊 使用 {len(test_images)} 张图片进行测试")
    
    # 预热模型（第一次推理通常较慢）
    print("🔥 预热模型...")
    _ = tool.extract_feature(test_images[0])
    
    # 测试特征提取时间
    extraction_times = []
    
    print("⏱️  测试特征提取时间...")
    for i, image_path in enumerate(test_images):
        start_time = time.time()
        feature = tool.extract_feature(image_path)
        end_time = time.time()
        
        extraction_time = (end_time - start_time) * 1000  # 转换为毫秒
        extraction_times.append(extraction_time)
        
        print(f"  图片 {i+1}: {extraction_time:.2f}ms")
    
    # 统计结果
    avg_time = statistics.mean(extraction_times)
    min_time = min(extraction_times)
    max_time = max(extraction_times)
    std_time = statistics.stdev(extraction_times) if len(extraction_times) > 1 else 0
    
    print(f"\n📈 特征提取时间统计:")
    print(f"  平均时间: {avg_time:.2f}ms")
    print(f"  最短时间: {min_time:.2f}ms")
    print(f"  最长时间: {max_time:.2f}ms")
    print(f"  标准差: {std_time:.2f}ms")
    print(f"  每秒处理: {1000/avg_time:.1f} 张图片")
    
    return avg_time, test_images, tool

def test_similarity_computation_time():
    """测试相似度计算时间"""
    print("\n=== 相似度计算时间测试 ===")
    
    # 初始化工具
    tool = VehicleRecognitionTool(
        config_file="configs/VERIWild/bagtricks_R50-ibn.yml",
        model_weights="veriwild_bot_R50-ibn.pth",
        similarity_threshold=0.6
    )
    
    # 准备测试特征
    query_dir = "datasets/veri/image_query"
    test_images = []
    
    if os.path.exists(query_dir):
        image_files = [f for f in os.listdir(query_dir) if f.endswith('.jpg')][:5]
        test_images = [os.path.join(query_dir, f) for f in image_files]
    
    if len(test_images) < 2:
        print("❌ 测试图片不足")
        return
    
    # 提取特征
    print("🔄 提取特征...")
    features = []
    for image_path in test_images:
        feature = tool.extract_feature(image_path)
        features.append(feature)
    
    # 测试相似度计算时间
    similarity_times = []
    
    print("⏱️  测试相似度计算时间...")
    for i in range(len(features)):
        for j in range(i+1, len(features)):
            start_time = time.time()
            similarity = tool.compute_similarity(features[i], features[j])
            end_time = time.time()
            
            similarity_time = (end_time - start_time) * 1000000  # 转换为微秒
            similarity_times.append(similarity_time)
            
            print(f"  特征 {i+1} vs {j+1}: {similarity_time:.2f}μs, 相似度: {similarity:.4f}")
    
    # 统计结果
    avg_time = statistics.mean(similarity_times)
    min_time = min(similarity_times)
    max_time = max(similarity_times)
    
    print(f"\n📈 相似度计算时间统计:")
    print(f"  平均时间: {avg_time:.2f}μs")
    print(f"  最短时间: {min_time:.2f}μs")
    print(f"  最长时间: {max_time:.2f}μs")
    print(f"  每秒计算: {1000000/avg_time:.0f} 次相似度")
    
    return avg_time

def test_database_query_time():
    """测试数据库查询时间"""
    print("\n=== 数据库查询时间测试 ===")
    
    # 初始化工具
    tool = VehicleRecognitionTool(
        config_file="configs/VERIWild/bagtricks_R50-ibn.yml",
        model_weights="veriwild_bot_R50-ibn.pth",
        database_path="performance_test.pkl",
        similarity_threshold=0.6
    )
    
    # 准备测试数据
    query_dir = "datasets/veri/image_query"
    test_images = []
    
    if os.path.exists(query_dir):
        image_files = [f for f in os.listdir(query_dir) if f.endswith('.jpg')][:20]
        test_images = [os.path.join(query_dir, f) for f in image_files]
    
    if len(test_images) < 10:
        print("❌ 测试图片不足")
        return
    
    # 构建不同大小的数据库
    database_sizes = [1, 5, 10, 15]
    
    for db_size in database_sizes:
        if db_size > len(test_images) - 1:
            continue
            
        print(f"\n📊 测试数据库大小: {db_size} 个车辆")
        
        # 清空数据库
        tool.vehicle_database = {}
        
        # 添加车辆到数据库
        for i in range(db_size):
            vehicle_id = f"vehicle_{i+1:03d}"
            tool.add_vehicle(test_images[i], vehicle_id, {"test": f"db_size_{db_size}"})
        
        # 测试查询时间
        query_image = test_images[db_size]  # 使用不在数据库中的图片
        query_times = []
        
        # 多次测试取平均值
        for _ in range(5):
            start_time = time.time()
            is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded(query_image)
            end_time = time.time()
            
            query_time = (end_time - start_time) * 1000  # 转换为毫秒
            query_times.append(query_time)
        
        avg_query_time = statistics.mean(query_times)
        print(f"  平均查询时间: {avg_query_time:.2f}ms")
        print(f"  每个车辆平均: {avg_query_time/db_size:.2f}ms")
    
    # 清理测试数据库
    if os.path.exists("performance_test.pkl"):
        os.remove("performance_test.pkl")

def test_end_to_end_performance():
    """测试端到端性能"""
    print("\n=== 端到端性能测试 ===")
    
    # 初始化工具
    tool = VehicleRecognitionTool(
        config_file="configs/VERIWild/bagtricks_R50-ibn.yml",
        model_weights="veriwild_bot_R50-ibn.pth",
        database_path="e2e_test.pkl",
        similarity_threshold=0.6
    )
    
    # 准备测试数据
    query_dir = "datasets/veri/image_query"
    test_images = []
    
    if os.path.exists(query_dir):
        image_files = [f for f in os.listdir(query_dir) if f.endswith('.jpg')][:10]
        test_images = [os.path.join(query_dir, f) for f in image_files]
    
    if len(test_images) < 5:
        print("❌ 测试图片不足")
        return
    
    # 测试添加车辆的时间
    print("⏱️  测试添加车辆时间...")
    add_times = []
    
    for i in range(3):
        vehicle_id = f"vehicle_{i+1}"
        
        start_time = time.time()
        success = tool.add_vehicle(test_images[i], vehicle_id, {"test": "e2e"})
        end_time = time.time()
        
        if success:
            add_time = (end_time - start_time) * 1000
            add_times.append(add_time)
            print(f"  添加车辆 {i+1}: {add_time:.2f}ms")
    
    # 测试查询时间
    print("⏱️  测试查询时间...")
    query_times = []
    
    for i in range(3, 6):
        if i >= len(test_images):
            break
            
        start_time = time.time()
        is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded(test_images[i])
        end_time = time.time()
        
        query_time = (end_time - start_time) * 1000
        query_times.append(query_time)
        print(f"  查询图片 {i+1}: {query_time:.2f}ms, 结果: {'匹配' if is_recorded else '未匹配'}")
    
    # 统计结果
    if add_times:
        avg_add_time = statistics.mean(add_times)
        print(f"\n📈 添加车辆平均时间: {avg_add_time:.2f}ms")
    
    if query_times:
        avg_query_time = statistics.mean(query_times)
        print(f"📈 查询平均时间: {avg_query_time:.2f}ms")
    
    # 清理测试数据库
    if os.path.exists("e2e_test.pkl"):
        os.remove("e2e_test.pkl")

def test_image_preprocessing_time():
    """测试图像预处理时间"""
    print("\n=== 图像预处理时间测试 ===")
    
    query_dir = "datasets/veri/image_query"
    test_image = None
    
    if os.path.exists(query_dir):
        image_files = [f for f in os.listdir(query_dir) if f.endswith('.jpg')]
        if image_files:
            test_image = os.path.join(query_dir, image_files[0])
    
    if not test_image:
        print("❌ 没有找到测试图片")
        return
    
    # 测试图像读取时间
    read_times = []
    for _ in range(10):
        start_time = time.time()
        image = cv2.imread(test_image)
        end_time = time.time()
        read_times.append((end_time - start_time) * 1000)
    
    avg_read_time = statistics.mean(read_times)
    print(f"📖 图像读取平均时间: {avg_read_time:.2f}ms")
    
    # 测试图像预处理时间（调整大小、归一化等）
    image = cv2.imread(test_image)
    preprocess_times = []
    
    for _ in range(10):
        start_time = time.time()
        # 模拟预处理步骤
        resized = cv2.resize(image, (256, 256))
        normalized = resized.astype(np.float32) / 255.0
        end_time = time.time()
        preprocess_times.append((end_time - start_time) * 1000)
    
    avg_preprocess_time = statistics.mean(preprocess_times)
    print(f"🔄 图像预处理平均时间: {avg_preprocess_time:.2f}ms")

def main():
    """主函数"""
    print("🚀 车辆识别工具性能测试")
    print("=" * 50)
    
    try:
        # 1. 测试图像预处理时间
        test_image_preprocessing_time()
        
        # 2. 测试特征提取时间
        test_feature_extraction_time()
        
        # 3. 测试相似度计算时间
        test_similarity_computation_time()
        
        # 4. 测试数据库查询时间
        test_database_query_time()
        
        # 5. 测试端到端性能
        test_end_to_end_performance()
        
        print("\n✅ 性能测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
