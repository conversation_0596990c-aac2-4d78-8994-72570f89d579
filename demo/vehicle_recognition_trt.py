# encoding: utf-8
"""
基于TensorRT的高性能车辆识别工具
"""

import os
import sys
import pickle
import logging
import time
from typing import List, Tuple, Optional, Dict, Any

import cv2
import numpy as np

sys.path.append('.')
sys.path.append('tools/deploy')

from fastreid.utils.logger import setup_logger

# 设置日志
setup_logger(name="fastreid")
logger = logging.getLogger('vehicle_recognition_trt')

class VehicleRecognitionTRT:
    """
    基于TensorRT的高性能车辆识别工具
    """
    
    def __init__(self, 
                 trt_engine_path: str,
                 database_path: str = "vehicle_database_trt.pkl",
                 similarity_threshold: float = 0.6,
                 input_size: Tuple[int, int] = (256, 256),
                 batch_size: int = 1):
        """
        初始化TensorRT车辆识别工具
        
        Args:
            trt_engine_path: TensorRT引擎文件路径
            database_path: 车辆特征数据库文件路径
            similarity_threshold: 相似度阈值
            input_size: 输入图片尺寸 (height, width)
            batch_size: 批处理大小
        """
        self.trt_engine_path = trt_engine_path
        self.database_path = database_path
        self.similarity_threshold = similarity_threshold
        self.input_size = input_size
        self.batch_size = batch_size
        
        # 初始化TensorRT引擎
        self._setup_trt_engine()
        
        # 加载车辆数据库
        self.vehicle_database = self._load_database()
        
        logger.info(f"TensorRT车辆识别工具初始化完成，数据库中已有 {len(self.vehicle_database)} 辆车辆记录")
    
    def _setup_trt_engine(self):
        """设置TensorRT引擎"""
        try:
            from trt_inference import TrtEngine
            
            if not os.path.exists(self.trt_engine_path):
                raise FileNotFoundError(f"TensorRT引擎文件不存在: {self.trt_engine_path}")
            
            self.trt_engine = TrtEngine(
                self.trt_engine_path, 
                batch_size=self.batch_size
            )
            
            logger.info(f"TensorRT引擎加载成功: {self.trt_engine_path}")
            
        except ImportError:
            raise ImportError("无法导入TensorRT模块，请确保已正确安装TensorRT和相关依赖")
        except Exception as e:
            raise RuntimeError(f"TensorRT引擎初始化失败: {e}")
    
    def _load_database(self) -> Dict[str, Dict[str, Any]]:
        """加载车辆特征数据库"""
        if os.path.exists(self.database_path):
            try:
                with open(self.database_path, 'rb') as f:
                    database = pickle.load(f)
                logger.info(f"成功加载车辆数据库: {self.database_path}")
                return database
            except Exception as e:
                logger.warning(f"加载数据库失败: {e}，将创建新的数据库")
                return {}
        else:
            logger.info("数据库文件不存在，创建新的数据库")
            return {}
    
    def _save_database(self):
        """保存车辆特征数据库"""
        try:
            os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
            
            with open(self.database_path, 'wb') as f:
                pickle.dump(self.vehicle_database, f)
            logger.info(f"数据库已保存到: {self.database_path}")
        except Exception as e:
            logger.error(f"保存数据库失败: {e}")
    
    def extract_feature(self, image_path: str) -> np.ndarray:
        """
        使用TensorRT引擎提取车辆特征
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            归一化的特征向量
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        # BGR转RGB
        image_rgb = image[:, :, ::-1]
        
        # 使用TensorRT引擎提取特征
        features = self.trt_engine.inference_on_images([image_rgb], new_size=self.input_size)
        
        # 确保特征是一维的
        if features.ndim > 1:
            features = features.flatten()
        
        return features
    
    def extract_features_batch(self, image_paths: List[str]) -> List[np.ndarray]:
        """
        批量提取特征（利用TensorRT的批处理能力）
        
        Args:
            image_paths: 图片路径列表
            
        Returns:
            特征向量列表
        """
        # 读取所有图片
        images = []
        valid_paths = []
        
        for image_path in image_paths:
            if os.path.exists(image_path):
                image = cv2.imread(image_path)
                if image is not None:
                    # BGR转RGB
                    image_rgb = image[:, :, ::-1]
                    images.append(image_rgb)
                    valid_paths.append(image_path)
                else:
                    logger.warning(f"无法读取图片: {image_path}")
            else:
                logger.warning(f"图片文件不存在: {image_path}")
        
        if not images:
            return []
        
        # 批量推理
        features = self.trt_engine.inference_on_images(images, new_size=self.input_size)
        
        # 分离每个图片的特征
        feature_list = []
        for i in range(len(images)):
            feature = features[i] if features.ndim > 1 else features
            if feature.ndim > 1:
                feature = feature.flatten()
            feature_list.append(feature)
        
        return feature_list
    
    def compute_similarity(self, feature1: np.ndarray, feature2: np.ndarray) -> float:
        """计算两个特征向量的相似度（余弦相似度）"""
        similarity = np.dot(feature1, feature2)
        return float(similarity)
    
    def is_vehicle_recorded(self, 
                           image_path: str, 
                           metadata_filter: Optional[Dict[str, Any]] = None) -> Tuple[bool, Optional[str], float]:
        """
        判断车辆是否已经记录
        
        Args:
            image_path: 车辆图片路径
            metadata_filter: 可选的元数据过滤条件
            
        Returns:
            (是否已记录, 匹配的车辆ID, 最高相似度分数)
        """
        # 提取查询图片的特征
        query_feature = self.extract_feature(image_path)
        
        if not self.vehicle_database:
            return False, None, 0.0
        
        # 与数据库中的所有车辆进行比较
        max_similarity = 0.0
        best_match_id = None
        
        for vehicle_id, vehicle_data in self.vehicle_database.items():
            # 如果有元数据过滤条件，先检查是否匹配
            if metadata_filter and not self._match_metadata(vehicle_data.get("metadata", {}), metadata_filter):
                continue
                
            stored_feature = vehicle_data["feature"]
            similarity = self.compute_similarity(query_feature, stored_feature)
            
            if similarity > max_similarity:
                max_similarity = similarity
                best_match_id = vehicle_id
        
        # 判断是否超过阈值
        is_recorded = max_similarity >= self.similarity_threshold
        
        logger.info(f"查询结果: 是否已记录={is_recorded}, 最佳匹配={best_match_id}, 相似度={max_similarity:.4f}")
        
        return is_recorded, best_match_id if is_recorded else None, max_similarity
    
    def _match_metadata(self, stored_metadata: Dict[str, Any], filter_metadata: Dict[str, Any]) -> bool:
        """检查存储的元数据是否匹配过滤条件"""
        for key, value in filter_metadata.items():
            if key not in stored_metadata:
                return False
            
            stored_value = stored_metadata[key]
            
            if isinstance(value, str) and isinstance(stored_value, str):
                if value.lower() != stored_value.lower():
                    return False
            elif isinstance(value, (list, tuple)):
                if stored_value not in value:
                    return False
            else:
                if value != stored_value:
                    return False
        
        return True
    
    def add_vehicle(self, 
                   image_path: str, 
                   vehicle_id: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加新车辆到数据库
        
        Args:
            image_path: 车辆图片路径
            vehicle_id: 车辆唯一标识符
            metadata: 车辆元数据
            
        Returns:
            是否添加成功
        """
        try:
            if vehicle_id in self.vehicle_database:
                logger.warning(f"车辆ID {vehicle_id} 已存在，将覆盖原有记录")
            
            # 提取特征
            feature = self.extract_feature(image_path)
            
            # 添加到数据库
            self.vehicle_database[vehicle_id] = {
                "feature": feature,
                "metadata": metadata or {},
                "image_path": image_path
            }
            
            # 保存数据库
            self._save_database()
            
            logger.info(f"成功添加车辆: {vehicle_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加车辆失败: {e}")
            return False
    
    def benchmark_performance(self, test_images: List[str], num_runs: int = 10) -> Dict[str, float]:
        """
        性能基准测试
        
        Args:
            test_images: 测试图片列表
            num_runs: 测试运行次数
            
        Returns:
            性能统计信息
        """
        if not test_images:
            logger.warning("没有提供测试图片")
            return {}
        
        # 过滤有效图片
        valid_images = [img for img in test_images if os.path.exists(img)]
        if not valid_images:
            logger.warning("没有有效的测试图片")
            return {}
        
        logger.info(f"开始性能测试，使用 {len(valid_images)} 张图片，运行 {num_runs} 次")
        
        # 预热
        _ = self.extract_feature(valid_images[0])
        
        # 单张图片推理时间测试
        single_times = []
        for _ in range(num_runs):
            start_time = time.time()
            _ = self.extract_feature(valid_images[0])
            end_time = time.time()
            single_times.append((end_time - start_time) * 1000)
        
        # 批量推理时间测试（如果支持）
        batch_times = []
        if len(valid_images) >= self.batch_size:
            batch_images = valid_images[:self.batch_size]
            for _ in range(num_runs):
                start_time = time.time()
                _ = self.extract_features_batch(batch_images)
                end_time = time.time()
                batch_times.append((end_time - start_time) * 1000)
        
        # 统计结果
        import statistics
        
        results = {
            "single_avg_time_ms": statistics.mean(single_times),
            "single_min_time_ms": min(single_times),
            "single_max_time_ms": max(single_times),
            "single_fps": 1000 / statistics.mean(single_times),
        }
        
        if batch_times:
            results.update({
                "batch_avg_time_ms": statistics.mean(batch_times),
                "batch_min_time_ms": min(batch_times),
                "batch_max_time_ms": max(batch_times),
                "batch_fps": self.batch_size * 1000 / statistics.mean(batch_times),
            })
        
        # 打印结果
        print(f"\n📈 TensorRT性能测试结果:")
        print(f"  单张推理平均时间: {results['single_avg_time_ms']:.2f}ms")
        print(f"  单张推理FPS: {results['single_fps']:.1f}")
        
        if batch_times:
            print(f"  批量推理平均时间: {results['batch_avg_time_ms']:.2f}ms")
            print(f"  批量推理FPS: {results['batch_fps']:.1f}")
        
        return results

def main():
    """示例用法"""
    # TensorRT引擎路径（需要先转换）
    trt_engine_path = "outputs/trt_model/vehicle_reid.engine"
    
    if not os.path.exists(trt_engine_path):
        print(f"❌ TensorRT引擎不存在: {trt_engine_path}")
        print("请先运行 demo/tensorrt_optimization_guide.py 转换模型")
        return
    
    # 初始化TensorRT工具
    tool = VehicleRecognitionTRT(
        trt_engine_path=trt_engine_path,
        database_path="vehicle_database_trt.pkl",
        similarity_threshold=0.6,
        input_size=(256, 256)
    )
    
    # 测试图片
    test_images = [
        "datasets/veri/image_query/0002_c002_00030600_0.jpg",
        "datasets/veri/image_query/0005_c002_00075750_0.jpg",
        "datasets/veri/image_query/0038_c001_00001200_0.jpg"
    ]
    
    # 性能测试
    tool.benchmark_performance(test_images)

if __name__ == "__main__":
    main()
