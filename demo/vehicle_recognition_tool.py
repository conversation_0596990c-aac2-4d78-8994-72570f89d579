# encoding: utf-8
"""
车辆识别工具类
基于 FastReID 实现车辆重识别功能
@author: Assistant
"""

import os
import sys
import pickle
import logging
from typing import List, Tuple, Optional, Dict, Any
from pathlib import Path

import cv2
import numpy as np
import torch
from torch.backends import cudnn

# 添加项目路径
sys.path.append('.')

from fastreid.config import get_cfg
from fastreid.utils.logger import setup_logger
from predictor import FeatureExtractionDemo

# 设置日志
setup_logger(name="fastreid")
logger = logging.getLogger('vehicle_recognition_tool')

class VehicleRecognitionTool:
    """
    车辆识别工具类
    用于判断输入的车辆图片是否已经在数据库中记录
    """
    
    def __init__(self,
                 config_file: str,
                 model_weights: str,
                 database_path: str = "vehicle_database.pkl",
                 similarity_threshold: float = 0.3,
                 parallel: bool = False):
        """
        初始化车辆识别工具
        
        Args:
            config_file: 配置文件路径
            model_weights: 模型权重文件路径
            database_path: 车辆特征数据库文件路径
            similarity_threshold: 相似度阈值，超过此值认为是同一车辆
            parallel: 是否使用并行处理
        """
        self.config_file = config_file
        self.model_weights = model_weights
        self.database_path = database_path
        self.similarity_threshold = similarity_threshold
        self.parallel = parallel
        
        # 初始化模型
        self._setup_model()
        
        # 加载或初始化车辆数据库
        self.vehicle_database = self._load_database()
        
        logger.info(f"车辆识别工具初始化完成，数据库中已有 {len(self.vehicle_database)} 辆车辆记录")
    
    def _setup_model(self):
        """设置模型配置和预测器"""
        # 设置配置
        cfg = get_cfg()
        cfg.merge_from_file(self.config_file)
        cfg.MODEL.WEIGHTS = self.model_weights
        cfg.MODEL.DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
        cfg.freeze()
        
        # 初始化预测器
        self.predictor = FeatureExtractionDemo(cfg, parallel=self.parallel)
        
        logger.info(f"模型加载完成，使用设备: {cfg.MODEL.DEVICE}")
    
    def _load_database(self) -> Dict[str, Dict[str, Any]]:
        """
        加载车辆特征数据库
        
        Returns:
            车辆数据库字典，格式为 {vehicle_id: {"feature": feature_vector, "metadata": {...}}}
        """
        if os.path.exists(self.database_path):
            try:
                with open(self.database_path, 'rb') as f:
                    database = pickle.load(f)
                logger.info(f"成功加载车辆数据库: {self.database_path}")
                return database
            except Exception as e:
                logger.warning(f"加载数据库失败: {e}，将创建新的数据库")
                return {}
        else:
            logger.info("数据库文件不存在，创建新的数据库")
            return {}
    
    def _save_database(self):
        """保存车辆特征数据库"""
        try:
            # 确保目录存在
            # os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
            
            with open(self.database_path, 'wb') as f:
                pickle.dump(self.vehicle_database, f)
            logger.info(f"数据库已保存到: {self.database_path}")
        except Exception as e:
            logger.error(f"保存数据库失败: {e}")
    
    def extract_feature(self, image_path: str) -> np.ndarray:
        """
        从图片中提取车辆特征
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            归一化的特征向量
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        # 提取特征
        feature = self.predictor.run_on_image(image)
        
        # 转换为numpy数组并归一化
        if isinstance(feature, torch.Tensor):
            feature = feature.cpu().numpy()
        
        # 确保特征是一维的
        if feature.ndim > 1:
            feature = feature.flatten()
        
        # L2归一化
        feature = feature / (np.linalg.norm(feature) + 1e-8)
        
        return feature
    
    def compute_similarity(self, feature1: np.ndarray, feature2: np.ndarray) -> float:
        """
        计算两个特征向量的相似度（余弦相似度）
        
        Args:
            feature1: 特征向量1
            feature2: 特征向量2
            
        Returns:
            相似度分数 (0-1之间，1表示完全相同)
        """
        # 计算余弦相似度
        similarity = np.dot(feature1, feature2)
        return float(similarity)
    
    def is_vehicle_recorded(self,
                           image_path: str,
                           metadata_filter: Optional[Dict[str, Any]] = None) -> Tuple[bool, Optional[str], float]:
        """
        判断车辆是否已经记录

        Args:
            image_path: 车辆图片路径
            metadata_filter: 可选的元数据过滤条件，如 {"color": "红色", "plate": "京A12345"}

        Returns:
            (是否已记录, 匹配的车辆ID, 最高相似度分数)
        """
        # 提取查询图片的特征
        query_feature = self.extract_feature(image_path)

        if not self.vehicle_database:
            return False, None, 0.0

        # 与数据库中的所有车辆进行比较
        max_similarity = 0.0
        best_match_id = None

        for vehicle_id, vehicle_data in self.vehicle_database.items():
            # 如果有元数据过滤条件，先检查是否匹配
            if metadata_filter and not self._match_metadata(vehicle_data.get("metadata", {}), metadata_filter):
                continue

            stored_feature = vehicle_data["feature"]
            similarity = self.compute_similarity(query_feature, stored_feature)

            if similarity > max_similarity:
                max_similarity = similarity
                best_match_id = vehicle_id

        # 判断是否超过阈值
        is_recorded = max_similarity >= self.similarity_threshold

        logger.info(f"查询结果: 是否已记录={is_recorded}, 最佳匹配={best_match_id}, 相似度={max_similarity:.4f}")

        return is_recorded, best_match_id if is_recorded else None, max_similarity

    def _match_metadata(self, stored_metadata: Dict[str, Any], filter_metadata: Dict[str, Any]) -> bool:
        """
        检查存储的元数据是否匹配过滤条件

        Args:
            stored_metadata: 数据库中存储的元数据
            filter_metadata: 过滤条件

        Returns:
            是否匹配
        """
        for key, value in filter_metadata.items():
            if key not in stored_metadata:
                return False

            stored_value = stored_metadata[key]

            # 支持不同类型的匹配
            if isinstance(value, str) and isinstance(stored_value, str):
                # 字符串匹配（不区分大小写）
                if value.lower() != stored_value.lower():
                    return False
            elif isinstance(value, (list, tuple)):
                # 列表匹配（值在列表中）
                if stored_value not in value:
                    return False
            else:
                # 精确匹配
                if value != stored_value:
                    return False

        return True

    def query_by_metadata(self, metadata_filter: Dict[str, Any]) -> List[str]:
        """
        根据元数据查询车辆

        Args:
            metadata_filter: 元数据过滤条件

        Returns:
            匹配的车辆ID列表
        """
        matching_vehicles = []

        for vehicle_id, vehicle_data in self.vehicle_database.items():
            stored_metadata = vehicle_data.get("metadata", {})
            if self._match_metadata(stored_metadata, metadata_filter):
                matching_vehicles.append(vehicle_id)

        return matching_vehicles

    def add_vehicle(self,
                   image_path: str, 
                   vehicle_id: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加新车辆到数据库
        
        Args:
            image_path: 车辆图片路径
            vehicle_id: 车辆唯一标识符
            metadata: 车辆元数据（如车牌号、颜色、型号等）
            
        Returns:
            是否添加成功
        """
        try:
            # 检查车辆ID是否已存在
            if vehicle_id in self.vehicle_database:
                logger.warning(f"车辆ID {vehicle_id} 已存在，将覆盖原有记录")
            
            # 提取特征
            feature = self.extract_feature(image_path)
            
            # 添加到数据库
            self.vehicle_database[vehicle_id] = {
                "feature": feature,
                "metadata": metadata or {},
                "image_path": image_path
            }
            
            # 保存数据库
            self._save_database()
            
            logger.info(f"成功添加车辆: {vehicle_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加车辆失败: {e}")
            return False
    
    def remove_vehicle(self, vehicle_id: str) -> bool:
        """
        从数据库中移除车辆
        
        Args:
            vehicle_id: 车辆ID
            
        Returns:
            是否移除成功
        """
        if vehicle_id in self.vehicle_database:
            del self.vehicle_database[vehicle_id]
            self._save_database()
            logger.info(f"成功移除车辆: {vehicle_id}")
            return True
        else:
            logger.warning(f"车辆ID {vehicle_id} 不存在")
            return False
    
    def get_vehicle_info(self, vehicle_id: str) -> Optional[Dict[str, Any]]:
        """
        获取车辆信息
        
        Args:
            vehicle_id: 车辆ID
            
        Returns:
            车辆信息字典或None
        """
        return self.vehicle_database.get(vehicle_id)
    
    def list_vehicles(self) -> List[str]:
        """
        列出所有已记录的车辆ID
        
        Returns:
            车辆ID列表
        """
        return list(self.vehicle_database.keys())
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "total_vehicles": len(self.vehicle_database),
            "database_path": self.database_path,
            "similarity_threshold": self.similarity_threshold
        }


def main():
    """示例用法"""
    # 配置参数
    config_file = "/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/config.yaml"
    model_weights = "/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/model_best.pth"
    
    # 初始化工具
    tool = VehicleRecognitionTool(
        config_file=config_file,
        model_weights=model_weights,
        database_path="vehicle_database.pkl",
        similarity_threshold=0.7
    )
    
    # 示例：添加车辆
    tool.add_vehicle("/home/<USER>/project/fast-reid/datasets/veri/image_test/0002_c004_00084250_0.jpg", "vehicle_001", {"plate": "京A12345", "color": "红色"})
    # tool.add_vehicle("", "vehicle_002", {"plate": "京A12345", "color": "红色"})
    # 示例：查询车辆
    is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded("/home/<USER>/project/fast-reid/datasets/veri/image_query/0002_c002_00030600_0.jpg")
    print(f"车辆是否已记录: {is_recorded}, 匹配ID: {vehicle_id}, 相似度: {similarity}")


if __name__ == "__main__":
    main()
