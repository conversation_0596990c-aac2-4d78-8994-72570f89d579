# encoding: utf-8
"""
VeRi数据集车辆识别测试脚本
使用调整后的相似度阈值进行测试
"""

import os
import sys
import random
from pathlib import Path

sys.path.append('.')

from vehicle_recognition_tool import VehicleRecognitionTool

def test_veri_recognition():
    """测试VeRi数据集的车辆识别效果"""
    print("=== VeRi数据集车辆识别测试 ===")
    
    # 初始化工具（使用较低的阈值）
    tool = VehicleRecognitionTool(
        config_file="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/config.yaml",
        model_weights="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/model_best.pth",
        database_path="veri_test_database.pkl",
        similarity_threshold=0.25  # 根据诊断结果调整
    )
    
    query_dir = "datasets/veri/image_query"
    test_dir = "datasets/veri/image_test"
    
    # 收集同一车辆的图片
    vehicle_groups = {}
    for filename in os.listdir(query_dir):
        if filename.endswith('.jpg'):
            vehicle_id = filename.split('_')[0]
            if vehicle_id not in vehicle_groups:
                vehicle_groups[vehicle_id] = []
            vehicle_groups[vehicle_id].append(os.path.join(query_dir, filename))
    
    # 如果test目录存在，也加入
    if os.path.exists(test_dir):
        for filename in os.listdir(test_dir):
            if filename.endswith('.jpg'):
                vehicle_id = filename.split('_')[0]
                if vehicle_id not in vehicle_groups:
                    vehicle_groups[vehicle_id] = []
                vehicle_groups[vehicle_id].append(os.path.join(test_dir, filename))
    
    # 选择有多张图片的车辆进行测试
    test_vehicles = []
    for vehicle_id, images in vehicle_groups.items():
        if len(images) >= 2:
            test_vehicles.append((vehicle_id, images))
    
    # 随机选择5个车辆进行测试
    random.seed(42)  # 固定随机种子
    test_vehicles = random.sample(test_vehicles, min(5, len(test_vehicles)))
    
    print(f"选择 {len(test_vehicles)} 个车辆进行测试")
    
    # 测试流程
    correct_matches = 0
    total_tests = 0
    
    for vehicle_id, images in test_vehicles:
        print(f"\n--- 测试车辆 {vehicle_id} ---")
        print(f"该车辆共有 {len(images)} 张图片")
        
        # 使用第一张图片添加到数据库
        ref_image = images[0]
        ref_filename = os.path.basename(ref_image)
        
        success = tool.add_vehicle(
            ref_image, 
            vehicle_id, 
            {"source": "veri_dataset", "ref_image": ref_filename}
        )
        
        if not success:
            print(f"❌ 添加车辆 {vehicle_id} 失败")
            continue
            
        print(f"✅ 添加参考图片: {ref_filename}")
        
        # 使用其他图片进行查询测试
        test_images = images[1:3]  # 最多测试2张
        
        for test_image in test_images:
            test_filename = os.path.basename(test_image)
            print(f"\n🔍 查询图片: {test_filename}")
            
            is_recorded, matched_id, similarity = tool.is_vehicle_recorded(test_image)
            
            total_tests += 1
            
            if is_recorded and matched_id == vehicle_id:
                correct_matches += 1
                print(f"✅ 正确匹配! 车辆ID: {matched_id}, 相似度: {similarity:.4f}")
            elif is_recorded:
                print(f"❌ 错误匹配! 预期: {vehicle_id}, 实际: {matched_id}, 相似度: {similarity:.4f}")
            else:
                print(f"❌ 未找到匹配! 相似度: {similarity:.4f}")
    
    # 输出测试结果
    accuracy = correct_matches / total_tests if total_tests > 0 else 0
    print(f"\n=== 测试结果 ===")
    print(f"总测试次数: {total_tests}")
    print(f"正确匹配: {correct_matches}")
    print(f"准确率: {accuracy:.2%}")
    
    # 显示数据库状态
    stats = tool.get_database_stats()
    print(f"\n数据库统计:")
    print(f"  车辆总数: {stats['total_vehicles']}")
    print(f"  相似度阈值: {stats['similarity_threshold']}")

def test_different_thresholds():
    """测试不同相似度阈值的效果"""
    print("\n=== 测试不同相似度阈值 ===")
    
    query_dir = "datasets/veri/image_query"
    
    # 选择一个有多张图片的车辆
    vehicle_id = "0002"
    vehicle_images = []
    for filename in os.listdir(query_dir):
        if filename.startswith(f"{vehicle_id}_") and filename.endswith('.jpg'):
            vehicle_images.append(os.path.join(query_dir, filename))
    
    if len(vehicle_images) < 2:
        print("没有足够的测试图片")
        return
    
    ref_image = vehicle_images[0]
    test_image = vehicle_images[1]
    
    print(f"参考图片: {os.path.basename(ref_image)}")
    print(f"测试图片: {os.path.basename(test_image)}")
    
    # 测试不同阈值
    thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4]
    
    for threshold in thresholds:
        tool = VehicleRecognitionTool(
            config_file="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/config.yaml",
            model_weights="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/model_best.pth",
            database_path=f"temp_db_{threshold}.pkl",
            similarity_threshold=threshold
        )
        
        # 添加参考图片
        tool.add_vehicle(ref_image, vehicle_id, {"test": "threshold"})
        
        # 查询测试图片
        is_recorded, matched_id, similarity = tool.is_vehicle_recorded(test_image)
        
        status = "✅ 匹配" if is_recorded else "❌ 未匹配"
        print(f"阈值 {threshold:.2f}: {status}, 相似度: {similarity:.4f}")
        
        # 清理临时数据库
        temp_db = f"temp_db_{threshold}.pkl"
        if os.path.exists(temp_db):
            os.remove(temp_db)

def main():
    """主函数"""
    print("VeRi数据集车辆识别测试")
    print("=" * 50)
    
    try:
        # 1. 测试不同阈值效果
        test_different_thresholds()
        
        # 2. 完整的识别测试
        test_veri_recognition()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
