# TensorRT优化指南

使用TensorRT加速FastReID车辆识别模型，获得更快的推理速度。

## 🚀 性能提升预期

| 优化方式 | 预期加速比 | 精度损失 | 适用场景 |
|---------|-----------|---------|---------|
| **FP16** | 1.5-2x | 几乎无 | 生产环境推荐 |
| **INT8** | 2-4x | 轻微 | 对精度要求不高的场景 |
| **FP32** | 1.2-1.5x | 无 | 基准对比 |

## 📋 前置要求

### 1. 硬件要求
- **GPU**: NVIDIA GPU (支持CUDA)
- **显存**: 至少2GB
- **推荐**: RTX 20系列及以上（支持Tensor Core）

### 2. 软件依赖
```bash
# 安装TensorRT (根据CUDA版本选择)
pip install nvidia-tensorrt

# 安装其他依赖
pip install onnx onnxoptimizer onnxsim pycuda
```

### 3. 检查依赖
```bash
python demo/tensorrt_optimization_guide.py --test-only
```

## 🔧 完整优化流程

### 步骤1: 导出ONNX模型
```bash
python tools/deploy/onnx_export.py \
    --config-file configs/VERIWild/bagtricks_R50-ibn.yml \
    --name vehicle_reid \
    --output outputs/onnx_model \
    MODEL.WEIGHTS veriwild_bot_R50-ibn.pth
```

### 步骤2: 转换TensorRT引擎
```bash
# FP16模式（推荐）
python tools/deploy/trt_export.py \
    --onnx-model outputs/onnx_model/vehicle_reid.onnx \
    --name vehicle_reid \
    --output outputs/trt_model \
    --mode fp16 \
    --batch-size 1 \
    --height 256 \
    --width 256

# INT8模式（最快）
python tools/deploy/trt_export.py \
    --onnx-model outputs/onnx_model/vehicle_reid.onnx \
    --name vehicle_reid \
    --output outputs/trt_model \
    --mode int8 \
    --batch-size 1 \
    --height 256 \
    --width 256
```

### 步骤3: 性能测试
```bash
python demo/tensorrt_optimization_guide.py --test-only
```

## 🛠️ 一键优化工具

使用我们提供的一键优化脚本：

```bash
# 完整流程（ONNX导出 + TensorRT转换 + 性能测试）
python demo/tensorrt_optimization_guide.py \
    --config configs/VERIWild/bagtricks_R50-ibn.yml \
    --weights veriwild_bot_R50-ibn.pth \
    --mode fp16

# 仅测试现有TensorRT引擎
python demo/tensorrt_optimization_guide.py --test-only
```

### 参数说明
- `--mode`: 精度模式 (`fp32`, `fp16`, `int8`)
- `--batch-size`: 批处理大小
- `--height/width`: 输入图片尺寸
- `--test-only`: 仅测试现有引擎

## 📊 性能对比

### 基准测试结果（示例）

| 模型类型 | 平均推理时间 | FPS | 加速比 |
|---------|-------------|-----|-------|
| PyTorch | 18.47ms | 54.1 | 1.0x |
| TensorRT FP16 | 8-12ms | 80-125 | 1.5-2.3x |
| TensorRT INT8 | 5-8ms | 125-200 | 2.3-3.7x |

*实际性能取决于具体硬件配置*

## 🔧 使用TensorRT优化的工具

### 基本用法
```python
from demo.vehicle_recognition_trt import VehicleRecognitionTRT

# 初始化TensorRT工具
tool = VehicleRecognitionTRT(
    trt_engine_path="outputs/trt_model/vehicle_reid.engine",
    similarity_threshold=0.6,
    input_size=(256, 256)
)

# 添加车辆
tool.add_vehicle("car1.jpg", "vehicle_001", {"plate": "京A12345"})

# 查询车辆
is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded("query.jpg")
print(f"是否已记录: {is_recorded}, 相似度: {similarity:.4f}")
```

### 批量处理
```python
# 批量提取特征（更高效）
image_paths = ["car1.jpg", "car2.jpg", "car3.jpg"]
features = tool.extract_features_batch(image_paths)
```

### 性能测试
```python
# 性能基准测试
test_images = ["test1.jpg", "test2.jpg", "test3.jpg"]
results = tool.benchmark_performance(test_images, num_runs=10)
```

## ⚠️ 注意事项

### 1. 模型兼容性
- 确保ONNX导出和TensorRT转换使用相同的输入尺寸
- VERIWild模型推荐使用256x256输入尺寸

### 2. 精度vs速度权衡
- **FP16**: 最佳平衡，推荐生产环境使用
- **INT8**: 最快速度，需要校准数据集
- **FP32**: 基准精度，速度提升有限

### 3. 批处理优化
- 批处理大小越大，吞吐量越高
- 但会增加延迟和显存占用
- 实时应用推荐batch_size=1

### 4. 显存管理
- TensorRT引擎会占用显存
- 多个引擎同时运行需要足够显存
- 可以通过动态形状减少显存占用

## 🐛 故障排除

### 常见问题

#### 1. TensorRT导入失败
```bash
# 检查CUDA版本
nvidia-smi

# 重新安装对应版本的TensorRT
pip uninstall nvidia-tensorrt
pip install nvidia-tensorrt
```

#### 2. ONNX导出失败
- 检查模型权重文件是否存在
- 确认配置文件路径正确
- 查看错误日志定位问题

#### 3. TensorRT转换失败
- 检查ONNX模型是否有效
- 确认GPU支持所选精度模式
- 尝试降低批处理大小

#### 4. 推理结果不一致
- 检查输入预处理是否一致
- 确认模型输入尺寸匹配
- 对比ONNX和TensorRT的输出

### 调试技巧

1. **逐步验证**:
   ```bash
   # 1. 先测试ONNX导出
   python tools/deploy/onnx_inference.py --onnx-model outputs/onnx_model/vehicle_reid.onnx
   
   # 2. 再测试TensorRT转换
   python tools/deploy/trt_inference.py --model-path outputs/trt_model/vehicle_reid.engine
   ```

2. **性能分析**:
   ```bash
   # 使用nsight systems分析性能
   nsys profile python demo/vehicle_recognition_trt.py
   ```

3. **精度验证**:
   ```python
   # 对比PyTorch和TensorRT的输出
   pytorch_feature = pytorch_tool.extract_feature("test.jpg")
   trt_feature = trt_tool.extract_feature("test.jpg")
   diff = np.abs(pytorch_feature - trt_feature).mean()
   print(f"特征差异: {diff}")
   ```

## 📈 进阶优化

### 1. 动态形状
支持不同输入尺寸的模型：
```bash
python tools/deploy/trt_export.py \
    --onnx-model model.onnx \
    --dynamic-shapes \
    --min-shapes 1,3,224,224 \
    --opt-shapes 1,3,256,256 \
    --max-shapes 1,3,512,512
```

### 2. 多流并行
利用CUDA流并行处理：
```python
# 创建多个TensorRT引擎实例
engines = [VehicleRecognitionTRT(engine_path) for _ in range(4)]

# 并行处理
import concurrent.futures
with concurrent.futures.ThreadPoolExecutor() as executor:
    futures = [executor.submit(engine.extract_feature, img) for engine, img in zip(engines, images)]
    results = [f.result() for f in futures]
```

### 3. 量化感知训练
获得更好的INT8精度：
```bash
# 使用量化感知训练的模型
python tools/deploy/trt_export.py \
    --onnx-model qat_model.onnx \
    --mode int8 \
    --qat-model
```

## 🎯 最佳实践

1. **开发阶段**: 使用PyTorch模型快速迭代
2. **测试阶段**: 使用FP16 TensorRT验证性能
3. **生产部署**: 根据需求选择FP16或INT8
4. **监控**: 定期检查推理时间和精度指标

## 📚 参考资源

- [TensorRT官方文档](https://docs.nvidia.com/deeplearning/tensorrt/)
- [ONNX官方文档](https://onnx.ai/)
- [FastReID部署指南](https://github.com/JDAI-CV/fast-reid/tree/master/tools/deploy)
