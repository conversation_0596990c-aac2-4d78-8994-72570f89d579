# encoding: utf-8
"""
TensorRT优化指南和工具
将FastReID模型转换为TensorRT引擎以获得更快的推理速度
"""

import os
import sys
import time
import subprocess
import argparse
from pathlib import Path

sys.path.append('.')

def check_dependencies():
    """检查TensorRT相关依赖"""
    print("=== 检查TensorRT依赖 ===")
    
    dependencies = {
        "tensorrt": "TensorRT",
        "onnx": "ONNX",
        "onnxoptimizer": "ONNX Optimizer", 
        "onnxsim": "ONNX Simplifier",
        "pycuda": "PyCUDA"
    }
    
    missing_deps = []
    
    for module, name in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {name}: 已安装")
        except ImportError:
            print(f"❌ {name}: 未安装")
            missing_deps.append(module)
    
    if missing_deps:
        print(f"\n⚠️  缺少依赖: {', '.join(missing_deps)}")
        print("请安装缺少的依赖:")
        for dep in missing_deps:
            if dep == "tensorrt":
                print(f"  pip install nvidia-tensorrt")
            elif dep == "pycuda":
                print(f"  pip install pycuda")
            else:
                print(f"  pip install {dep}")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def export_to_onnx(config_file, model_weights, output_dir="outputs/onnx_model", model_name="vehicle_reid"):
    """导出模型到ONNX格式"""
    print(f"\n=== 导出ONNX模型 ===")
    
    # 检查输入文件
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return None
        
    if not os.path.exists(model_weights):
        print(f"❌ 模型权重不存在: {model_weights}")
        return None
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建导出命令
    cmd = [
        "python", "tools/deploy/onnx_export.py",
        "--config-file", config_file,
        "--name", model_name,
        "--output", output_dir,
        "--batch-size", "1",
        "MODEL.WEIGHTS", model_weights
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            onnx_path = os.path.join(output_dir, f"{model_name}.onnx")
            print(f"✅ ONNX模型导出成功: {onnx_path}")
            return onnx_path
        else:
            print(f"❌ ONNX导出失败:")
            print(f"错误输出: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 导出过程出错: {e}")
        return None

def convert_to_tensorrt(onnx_path, output_dir="outputs/trt_model", model_name="vehicle_reid", 
                       mode="fp16", batch_size=1, height=256, width=256):
    """将ONNX模型转换为TensorRT引擎"""
    print(f"\n=== 转换TensorRT引擎 ===")
    
    if not os.path.exists(onnx_path):
        print(f"❌ ONNX文件不存在: {onnx_path}")
        return None
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建转换命令
    cmd = [
        "python", "tools/deploy/trt_export.py",
        "--onnx-model", onnx_path,
        "--name", model_name,
        "--output", output_dir,
        "--mode", mode,
        "--batch-size", str(batch_size),
        "--height", str(height),
        "--width", str(width)
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print(f"转换模式: {mode}")
    print(f"输入尺寸: {batch_size}x3x{height}x{width}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            trt_path = os.path.join(output_dir, f"{model_name}.engine")
            print(f"✅ TensorRT引擎转换成功: {trt_path}")
            return trt_path
        else:
            print(f"❌ TensorRT转换失败:")
            print(f"错误输出: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        return None

def benchmark_tensorrt_performance(trt_engine_path, test_images, batch_size=1):
    """测试TensorRT引擎性能"""
    print(f"\n=== TensorRT性能测试 ===")
    
    if not os.path.exists(trt_engine_path):
        print(f"❌ TensorRT引擎不存在: {trt_engine_path}")
        return
    
    try:
        # 导入TensorRT推理类
        sys.path.append('tools/deploy')
        from trt_inference import TrtEngine
        
        # 初始化TensorRT引擎
        print("🔄 初始化TensorRT引擎...")
        trt_engine = TrtEngine(trt_engine_path, batch_size=batch_size)
        
        # 准备测试图片
        import cv2
        import numpy as np
        
        test_imgs = []
        for img_path in test_images[:5]:  # 测试前5张图片
            if os.path.exists(img_path):
                img = cv2.imread(img_path)
                if img is not None:
                    # BGR转RGB
                    img_rgb = img[:, :, ::-1]
                    test_imgs.append(img_rgb)
        
        if not test_imgs:
            print("❌ 没有有效的测试图片")
            return
        
        print(f"📊 使用 {len(test_imgs)} 张图片进行测试")
        
        # 预热
        print("🔥 预热引擎...")
        _ = trt_engine.inference_on_images([test_imgs[0]])
        
        # 性能测试
        print("⏱️  测试推理时间...")
        inference_times = []
        
        for i, img in enumerate(test_imgs):
            start_time = time.time()
            features = trt_engine.inference_on_images([img])
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            inference_times.append(inference_time)
            
            print(f"  图片 {i+1}: {inference_time:.2f}ms, 特征维度: {features.shape}")
        
        # 统计结果
        import statistics
        avg_time = statistics.mean(inference_times)
        min_time = min(inference_times)
        max_time = max(inference_times)
        
        print(f"\n📈 TensorRT性能统计:")
        print(f"  平均推理时间: {avg_time:.2f}ms")
        print(f"  最短时间: {min_time:.2f}ms")
        print(f"  最长时间: {max_time:.2f}ms")
        print(f"  每秒处理: {1000/avg_time:.1f} 张图片")
        
        return avg_time
        
    except ImportError as e:
        print(f"❌ 导入TensorRT模块失败: {e}")
        print("请确保已正确安装TensorRT和相关依赖")
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def compare_performance():
    """比较PyTorch和TensorRT的性能"""
    print(f"\n=== 性能对比 ===")
    
    # 这里可以添加PyTorch vs TensorRT的性能对比
    # 需要先运行PyTorch模型测试，然后运行TensorRT测试
    
    print("📊 性能对比建议:")
    print("1. 先运行 demo/test_prediction_time.py 获取PyTorch基准性能")
    print("2. 转换为TensorRT引擎")
    print("3. 运行TensorRT性能测试")
    print("4. 对比两者的推理时间")

def main():
    """主函数 - TensorRT优化完整流程"""
    parser = argparse.ArgumentParser(description="TensorRT优化工具")
    parser.add_argument("--config", default="configs/VERIWild/bagtricks_R50-ibn.yml", help="配置文件路径")
    parser.add_argument("--weights", default="veriwild_bot_R50-ibn.pth", help="模型权重路径")
    parser.add_argument("--mode", default="fp16", choices=["fp32", "fp16", "int8"], help="TensorRT精度模式")
    parser.add_argument("--batch-size", type=int, default=1, help="批处理大小")
    parser.add_argument("--height", type=int, default=256, help="输入图片高度")
    parser.add_argument("--width", type=int, default=256, help="输入图片宽度")
    parser.add_argument("--test-only", action="store_true", help="仅测试现有TensorRT引擎")
    
    args = parser.parse_args()
    
    print("🚀 TensorRT优化工具")
    print("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        print("❌ 请先安装必要的依赖")
        return
    
    # 2. 如果只是测试现有引擎
    if args.test_only:
        trt_engine_path = f"outputs/trt_model/vehicle_reid.engine"
        test_images = [
            "datasets/veri/image_query/0002_c002_00030600_0.jpg",
            "datasets/veri/image_query/0005_c002_00075750_0.jpg",
            "datasets/veri/image_query/0038_c001_00001200_0.jpg"
        ]
        benchmark_tensorrt_performance(trt_engine_path, test_images, args.batch_size)
        return
    
    # 3. 完整的转换流程
    print(f"\n🎯 开始TensorRT优化流程")
    print(f"配置文件: {args.config}")
    print(f"模型权重: {args.weights}")
    print(f"精度模式: {args.mode}")
    
    # 导出ONNX
    onnx_path = export_to_onnx(args.config, args.weights)
    if not onnx_path:
        print("❌ ONNX导出失败，终止流程")
        return
    
    # 转换TensorRT
    trt_path = convert_to_tensorrt(
        onnx_path, 
        mode=args.mode,
        batch_size=args.batch_size,
        height=args.height,
        width=args.width
    )
    if not trt_path:
        print("❌ TensorRT转换失败，终止流程")
        return
    
    # 性能测试
    test_images = [
        "datasets/veri/image_query/0002_c002_00030600_0.jpg",
        "datasets/veri/image_query/0005_c002_00075750_0.jpg",
        "datasets/veri/image_query/0038_c001_00001200_0.jpg"
    ]
    
    benchmark_tensorrt_performance(trt_path, test_images, args.batch_size)
    
    print(f"\n✅ TensorRT优化完成!")
    print(f"ONNX模型: {onnx_path}")
    print(f"TensorRT引擎: {trt_path}")

if __name__ == "__main__":
    main()
