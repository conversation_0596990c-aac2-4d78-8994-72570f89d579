# encoding: utf-8
"""
VeRi数据集匹配问题诊断脚本
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

sys.path.append('.')

from vehicle_recognition_tool import VehicleRecognitionTool

def analyze_veri_dataset():
    """分析VeRi数据集的文件名格式"""
    query_dir = "datasets/veri/image_query"
    test_dir = "datasets/veri/image_test"
    
    print("=== VeRi数据集分析 ===")
    
    # 分析query目录
    query_files = sorted([f for f in os.listdir(query_dir) if f.endswith('.jpg')])[:10]
    print(f"\nQuery目录示例文件 (前10个):")
    for f in query_files:
        # 解析文件名: vehicleID_cameraID_frameID_trackID.jpg
        parts = f.replace('.jpg', '').split('_')
        if len(parts) >= 4:
            vehicle_id, camera_id, frame_id, track_id = parts[:4]
            print(f"  {f} -> 车辆ID: {vehicle_id}, 摄像头: {camera_id}")
    
    # 分析test目录
    if os.path.exists(test_dir):
        test_files = sorted([f for f in os.listdir(test_dir) if f.endswith('.jpg')])[:10]
        print(f"\nTest目录示例文件 (前10个):")
        for f in test_files:
            parts = f.replace('.jpg', '').split('_')
            if len(parts) >= 4:
                vehicle_id, camera_id, frame_id, track_id = parts[:4]
                print(f"  {f} -> 车辆ID: {vehicle_id}, 摄像头: {camera_id}")
    
    return query_files, test_files if os.path.exists(test_dir) else []

def test_same_vehicle_similarity():
    """测试同一车辆不同摄像头的相似度"""
    print("\n=== 测试同一车辆的相似度 ===")
    
    # 初始化工具
    tool = VehicleRecognitionTool(
        config_file="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/config.yaml",
        model_weights="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/model_best.pth",
        similarity_threshold=0.5  # 降低阈值进行测试
    )
    
    query_dir = "datasets/veri/image_query"
    
    # 找到同一车辆的不同图片
    vehicle_groups = {}
    for filename in os.listdir(query_dir):
        if filename.endswith('.jpg'):
            vehicle_id = filename.split('_')[0]
            if vehicle_id not in vehicle_groups:
                vehicle_groups[vehicle_id] = []
            vehicle_groups[vehicle_id].append(filename)
    
    # 测试前几个有多张图片的车辆
    test_count = 0
    for vehicle_id, files in vehicle_groups.items():
        if len(files) >= 2 and test_count < 3:  # 只测试前3个车辆
            print(f"\n--- 测试车辆 {vehicle_id} ---")
            
            # 使用第一张图片作为参考
            ref_image = os.path.join(query_dir, files[0])
            ref_feature = tool.extract_feature(ref_image)
            print(f"参考图片: {files[0]}")
            
            # 测试其他图片的相似度
            for i, test_file in enumerate(files[1:3], 1):  # 最多测试2张
                test_image = os.path.join(query_dir, test_file)
                test_feature = tool.extract_feature(test_image)
                similarity = tool.compute_similarity(ref_feature, test_feature)
                
                print(f"  vs {test_file}: 相似度 = {similarity:.4f}")
                
                if i >= 2:  # 限制测试数量
                    break
            
            test_count += 1

def test_different_vehicle_similarity():
    """测试不同车辆的相似度"""
    print("\n=== 测试不同车辆的相似度 ===")
    
    tool = VehicleRecognitionTool(
        config_file="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/config.yaml",
        model_weights="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/model_best.pth",
        similarity_threshold=0.5
    )
    
    query_dir = "datasets/veri/image_query"
    
    # 获取不同车辆的图片
    vehicle_files = {}
    for filename in os.listdir(query_dir):
        if filename.endswith('.jpg'):
            vehicle_id = filename.split('_')[0]
            if vehicle_id not in vehicle_files:
                vehicle_files[vehicle_id] = filename
    
    # 取前5个不同车辆进行测试
    vehicle_ids = list(vehicle_files.keys())[:5]
    
    print(f"测试车辆: {vehicle_ids}")
    
    # 提取特征
    features = {}
    for vehicle_id in vehicle_ids:
        image_path = os.path.join(query_dir, vehicle_files[vehicle_id])
        features[vehicle_id] = tool.extract_feature(image_path)
        print(f"车辆 {vehicle_id}: {vehicle_files[vehicle_id]}")
    
    # 计算两两相似度
    print(f"\n相似度矩阵:")
    print("车辆ID", end="")
    for vid in vehicle_ids:
        print(f"\t{vid}", end="")
    print()
    
    for i, vid1 in enumerate(vehicle_ids):
        print(f"{vid1}", end="")
        for j, vid2 in enumerate(vehicle_ids):
            if i <= j:
                similarity = tool.compute_similarity(features[vid1], features[vid2])
                print(f"\t{similarity:.3f}", end="")
            else:
                print(f"\t-", end="")
        print()

def test_feature_statistics():
    """分析特征向量的统计信息"""
    print("\n=== 特征向量统计分析 ===")
    
    tool = VehicleRecognitionTool(
        config_file="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/config.yaml",
        model_weights="/home/<USER>/project/fast-reid/logs/veri/sbs_R50-ibn/model_best.pth",
        similarity_threshold=0.5
    )
    
    query_dir = "datasets/veri/image_query"
    
    # 随机选择几张图片
    files = [f for f in os.listdir(query_dir) if f.endswith('.jpg')][:5]
    
    features = []
    for filename in files:
        image_path = os.path.join(query_dir, filename)
        feature = tool.extract_feature(image_path)
        features.append(feature)
        
        print(f"{filename}:")
        print(f"  特征维度: {feature.shape}")
        print(f"  特征范围: [{feature.min():.4f}, {feature.max():.4f}]")
        print(f"  特征均值: {feature.mean():.4f}")
        print(f"  特征标准差: {feature.std():.4f}")
        print(f"  L2范数: {np.linalg.norm(feature):.4f}")
        print()

def test_image_preprocessing():
    """测试图片预处理"""
    print("\n=== 图片预处理测试 ===")
    
    query_dir = "datasets/veri/image_query"
    test_file = "0002_c002_00030600_0.jpg"
    image_path = os.path.join(query_dir, test_file)
    
    if os.path.exists(image_path):
        # 读取原始图片
        image = cv2.imread(image_path)
        print(f"原始图片: {test_file}")
        print(f"  尺寸: {image.shape}")
        print(f"  数据类型: {image.dtype}")
        print(f"  像素值范围: [{image.min()}, {image.max()}]")
        
        # 模拟预处理过程
        # BGR转RGB
        image_rgb = image[:, :, ::-1]
        print(f"BGR->RGB后: 像素值范围: [{image_rgb.min()}, {image_rgb.max()}]")
        
        # 调整大小 (假设目标尺寸是256x256)
        resized = cv2.resize(image_rgb, (256, 256), interpolation=cv2.INTER_CUBIC)
        print(f"调整大小后: {resized.shape}")
        
        # 转换为float32并归一化到[0,1]
        normalized = resized.astype("float32") / 255.0
        print(f"归一化后: 范围[{normalized.min():.4f}, {normalized.max():.4f}]")

def main():
    """主函数"""
    print("VeRi数据集匹配问题诊断")
    print("=" * 50)
    
    try:
        # 1. 分析数据集结构
        analyze_veri_dataset()
        
        # 2. 测试图片预处理
        test_image_preprocessing()
        
        # 3. 测试特征统计
        test_feature_statistics()
        
        # 4. 测试同一车辆的相似度
        test_same_vehicle_similarity()
        
        # 5. 测试不同车辆的相似度
        test_different_vehicle_similarity()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
