# encoding: utf-8
"""
车辆识别工具使用示例
演示如何使用 VehicleRecognitionTool 进行车辆识别
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目路径
sys.path.append('.')

from vehicle_recognition_tool import VehicleRecognitionTool


def setup_args():
    """设置命令行参数"""
    parser = argparse.ArgumentParser(description="车辆识别工具示例")
    
    parser.add_argument(
        "--config-file",
        required=True,
        help="模型配置文件路径"
    )
    
    parser.add_argument(
        "--model-weights", 
        required=True,
        help="模型权重文件路径"
    )
    
    parser.add_argument(
        "--database-path",
        default="vehicle_database.pkl",
        help="车辆数据库文件路径"
    )
    
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.7,
        help="相似度阈值"
    )
    
    parser.add_argument(
        "--action",
        choices=["add", "query", "query-meta", "remove", "list", "stats"],
        required=True,
        help="操作类型: add(添加车辆), query(查询车辆), query-meta(根据元数据查询), remove(删除车辆), list(列出所有车辆), stats(统计信息)"
    )
    
    parser.add_argument(
        "--image-path",
        help="车辆图片路径 (add和query操作需要)"
    )
    
    parser.add_argument(
        "--vehicle-id",
        help="车辆ID (add和remove操作需要)"
    )
    
    parser.add_argument(
        "--metadata",
        help="车辆元数据，JSON格式字符串，如: '{\"plate\":\"京A12345\",\"color\":\"红色\"}'"
    )

    parser.add_argument(
        "--filter",
        help="元数据过滤条件，JSON格式字符串，用于query和query-meta操作"
    )
    
    return parser.parse_args()


def add_vehicle(tool, image_path, vehicle_id, metadata_str=None):
    """添加车辆到数据库"""
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在 - {image_path}")
        return False
    
    metadata = {}
    if metadata_str:
        try:
            import json
            metadata = json.loads(metadata_str)
        except json.JSONDecodeError:
            print(f"警告: 无法解析元数据JSON - {metadata_str}")
    
    success = tool.add_vehicle(image_path, vehicle_id, metadata)
    if success:
        print(f"✅ 成功添加车辆: {vehicle_id}")
        if metadata:
            print(f"   元数据: {metadata}")
    else:
        print(f"❌ 添加车辆失败: {vehicle_id}")
    
    return success


def query_vehicle(tool, image_path, metadata_filter=None):
    """查询车辆是否已记录"""
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在 - {image_path}")
        return

    print(f"🔍 正在查询车辆: {image_path}")
    if metadata_filter:
        print(f"   元数据过滤条件: {metadata_filter}")

    try:
        is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded(image_path, metadata_filter)

        print(f"查询结果:")
        print(f"  是否已记录: {'是' if is_recorded else '否'}")
        print(f"  相似度分数: {similarity:.4f}")

        if is_recorded and vehicle_id:
            print(f"  匹配车辆ID: {vehicle_id}")

            # 获取车辆详细信息
            vehicle_info = tool.get_vehicle_info(vehicle_id)
            if vehicle_info and vehicle_info.get("metadata"):
                print(f"  车辆信息: {vehicle_info['metadata']}")
        else:
            print(f"  未找到匹配的车辆记录")

    except Exception as e:
        print(f"❌ 查询失败: {e}")


def query_by_metadata(tool, metadata_filter):
    """根据元数据查询车辆"""
    print(f"🔍 根据元数据查询车辆: {metadata_filter}")

    try:
        matching_vehicles = tool.query_by_metadata(metadata_filter)

        if matching_vehicles:
            print(f"✅ 找到 {len(matching_vehicles)} 辆匹配的车辆:")
            for i, vehicle_id in enumerate(matching_vehicles, 1):
                vehicle_info = tool.get_vehicle_info(vehicle_id)
                print(f"  {i}. {vehicle_id}")
                if vehicle_info:
                    if vehicle_info.get("metadata"):
                        print(f"     信息: {vehicle_info['metadata']}")
                    if vehicle_info.get("image_path"):
                        print(f"     图片: {vehicle_info['image_path']}")
        else:
            print(f"❌ 未找到匹配的车辆")

    except Exception as e:
        print(f"❌ 查询失败: {e}")


def remove_vehicle(tool, vehicle_id):
    """从数据库中移除车辆"""
    success = tool.remove_vehicle(vehicle_id)
    if success:
        print(f"✅ 成功移除车辆: {vehicle_id}")
    else:
        print(f"❌ 移除失败，车辆ID不存在: {vehicle_id}")


def list_vehicles(tool):
    """列出所有车辆"""
    vehicles = tool.list_vehicles()
    
    if not vehicles:
        print("📝 数据库中暂无车辆记录")
        return
    
    print(f"📝 数据库中共有 {len(vehicles)} 辆车辆:")
    
    for i, vehicle_id in enumerate(vehicles, 1):
        vehicle_info = tool.get_vehicle_info(vehicle_id)
        print(f"  {i}. {vehicle_id}")
        
        if vehicle_info:
            if vehicle_info.get("metadata"):
                print(f"     信息: {vehicle_info['metadata']}")
            if vehicle_info.get("image_path"):
                print(f"     图片: {vehicle_info['image_path']}")


def show_stats(tool):
    """显示数据库统计信息"""
    stats = tool.get_database_stats()
    
    print("📊 数据库统计信息:")
    print(f"  车辆总数: {stats['total_vehicles']}")
    print(f"  数据库路径: {stats['database_path']}")
    print(f"  相似度阈值: {stats['similarity_threshold']}")


def main():
    """主函数"""
    args = setup_args()
    
    # 检查必要文件是否存在
    if not os.path.exists(args.config_file):
        print(f"错误: 配置文件不存在 - {args.config_file}")
        return
    
    if not os.path.exists(args.model_weights):
        print(f"错误: 模型权重文件不存在 - {args.model_weights}")
        return
    
    # 初始化工具
    print("🚀 正在初始化车辆识别工具...")
    try:
        tool = VehicleRecognitionTool(
            config_file=args.config_file,
            model_weights=args.model_weights,
            database_path=args.database_path,
            similarity_threshold=args.threshold
        )
        print("✅ 工具初始化完成")
    except Exception as e:
        print(f"❌ 工具初始化失败: {e}")
        return
    
    # 解析过滤条件
    metadata_filter = None
    if args.filter:
        try:
            import json
            metadata_filter = json.loads(args.filter)
        except json.JSONDecodeError:
            print(f"错误: 无法解析过滤条件JSON - {args.filter}")
            return

    # 执行相应操作
    if args.action == "add":
        if not args.image_path or not args.vehicle_id:
            print("错误: add操作需要指定 --image-path 和 --vehicle-id")
            return
        add_vehicle(tool, args.image_path, args.vehicle_id, args.metadata)

    elif args.action == "query":
        if not args.image_path:
            print("错误: query操作需要指定 --image-path")
            return
        query_vehicle(tool, args.image_path, metadata_filter)

    elif args.action == "query-meta":
        if not metadata_filter:
            print("错误: query-meta操作需要指定 --filter")
            return
        query_by_metadata(tool, metadata_filter)

    elif args.action == "remove":
        if not args.vehicle_id:
            print("错误: remove操作需要指定 --vehicle-id")
            return
        remove_vehicle(tool, args.vehicle_id)

    elif args.action == "list":
        list_vehicles(tool)

    elif args.action == "stats":
        show_stats(tool)


if __name__ == "__main__":
    main()
