# encoding: utf-8
"""
测试VERIWild模型的车辆识别效果
"""

import os
import sys

sys.path.append('.')

from vehicle_recognition_tool import VehicleRecognitionTool

def main():
    """测试VERIWild模型"""
    print("=== VERIWild模型测试 ===")
    
    # 初始化工具（使用VERIWild模型）
    tool = VehicleRecognitionTool(
        config_file="configs/VERIWild/bagtricks_R50-ibn.yml",
        model_weights="veriwild_bot_R50-ibn.pth",
        database_path="veriwild_test.pkl",
        similarity_threshold=0.6
    )
    
    print("✅ VERIWild模型初始化成功")
    
    # 测试图片路径
    ref_image = "datasets/veri/image_test/0002_c004_00084250_0.jpg"
    query_image = "datasets/veri/image_query/0002_c002_00030600_0.jpg"
    
    # 检查图片是否存在
    if not os.path.exists(ref_image):
        print(f"❌ 参考图片不存在: {ref_image}")
        return
        
    if not os.path.exists(query_image):
        print(f"❌ 查询图片不存在: {query_image}")
        return
    
    # 添加车辆到数据库
    print(f"\n📝 添加车辆到数据库...")
    success = tool.add_vehicle(
        ref_image, 
        "vehicle_001", 
        {"plate": "京A12345", "color": "红色", "model": "测试车辆"}
    )
    
    if success:
        print("✅ 成功添加车辆")
    else:
        print("❌ 添加车辆失败")
        return
    
    # 查询车辆
    print(f"\n🔍 查询车辆...")
    is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded(query_image)
    
    print(f"查询结果:")
    print(f"  是否已记录: {'是' if is_recorded else '否'}")
    print(f"  匹配车辆ID: {vehicle_id}")
    print(f"  相似度分数: {similarity:.4f}")
    print(f"  阈值设置: {tool.similarity_threshold}")
    
    if is_recorded:
        # 获取车辆详细信息
        vehicle_info = tool.get_vehicle_info(vehicle_id)
        if vehicle_info and vehicle_info.get("metadata"):
            print(f"  车辆信息: {vehicle_info['metadata']}")
    
    # 测试不同车辆
    print(f"\n🔍 测试不同车辆...")
    different_image = "datasets/veri/image_query/0005_c002_00075750_0.jpg"
    
    if os.path.exists(different_image):
        is_recorded2, vehicle_id2, similarity2 = tool.is_vehicle_recorded(different_image)
        print(f"不同车辆查询结果:")
        print(f"  是否已记录: {'是' if is_recorded2 else '否'}")
        print(f"  相似度分数: {similarity2:.4f}")
    
    # 显示数据库统计
    stats = tool.get_database_stats()
    print(f"\n📊 数据库统计:")
    print(f"  车辆总数: {stats['total_vehicles']}")
    print(f"  相似度阈值: {stats['similarity_threshold']}")

if __name__ == "__main__":
    main()
