# 车辆识别工具使用说明

基于 FastReID 实现的车辆重识别工具，可以判断输入的车辆图片是否已经在数据库中记录。

## 功能特性

- 🚗 **车辆特征提取**: 使用深度学习模型提取车辆图片的特征向量
- 🔍 **车辆查询**: 判断输入车辆是否已在数据库中记录
- 📝 **车辆管理**: 添加、删除、查看车辆记录
- 💾 **持久化存储**: 车辆特征数据库自动保存到本地文件
- ⚡ **高效匹配**: 基于余弦相似度的快速车辆匹配
- 🎯 **可调阈值**: 支持自定义相似度阈值

## 文件说明

- `vehicle_recognition_tool.py`: 核心工具类
- `vehicle_recognition_example.py`: 命令行使用示例
- `VEHICLE_RECOGNITION_README.md`: 使用说明文档

## 安装依赖

确保已安装以下依赖：

```bash
pip install torch torchvision opencv-python numpy pickle
```

## 快速开始

### 1. 基本用法

```python
from vehicle_recognition_tool import VehicleRecognitionTool

# 初始化工具
tool = VehicleRecognitionTool(
    config_file="path/to/config.yaml",
    model_weights="path/to/model.pth",
    database_path="vehicle_database.pkl",
    similarity_threshold=0.7
)

# 添加车辆到数据库
tool.add_vehicle(
    image_path="vehicle1.jpg", 
    vehicle_id="car_001",
    metadata={"plate": "京A12345", "color": "红色", "model": "奥迪A4"}
)

# 查询车辆是否已记录
is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded("query_vehicle.jpg")
print(f"是否已记录: {is_recorded}, 匹配ID: {vehicle_id}, 相似度: {similarity}")
```

### 2. 命令行使用

#### 添加车辆

```bash
python vehicle_recognition_example.py \
    --config-file /path/to/config.yaml \
    --model-weights /path/to/model.pth \
    --action add \
    --image-path vehicle1.jpg \
    --vehicle-id car_001 \
    --metadata '{"plate":"京A12345","color":"红色","model":"奥迪A4"}'
```

#### 查询车辆

```bash
python vehicle_recognition_example.py \
    --config-file /path/to/config.yaml \
    --model-weights /path/to/model.pth \
    --action query \
    --image-path query_vehicle.jpg
```

#### 列出所有车辆

```bash
python vehicle_recognition_example.py \
    --config-file /path/to/config.yaml \
    --model-weights /path/to/model.pth \
    --action list
```

#### 删除车辆

```bash
python vehicle_recognition_example.py \
    --config-file /path/to/config.yaml \
    --model-weights /path/to/model.pth \
    --action remove \
    --vehicle-id car_001
```

#### 查看统计信息

```bash
python vehicle_recognition_example.py \
    --config-file /path/to/config.yaml \
    --model-weights /path/to/model.pth \
    --action stats
```

## 参数说明

### VehicleRecognitionTool 参数

- `config_file`: 模型配置文件路径
- `model_weights`: 预训练模型权重文件路径
- `database_path`: 车辆特征数据库文件路径（默认: "vehicle_database.pkl"）
- `similarity_threshold`: 相似度阈值，超过此值认为是同一车辆（默认: 0.7）
- `parallel`: 是否使用并行处理（默认: False）

### 命令行参数

- `--config-file`: 模型配置文件路径（必需）
- `--model-weights`: 模型权重文件路径（必需）
- `--database-path`: 数据库文件路径（可选）
- `--threshold`: 相似度阈值（可选，默认0.7）
- `--action`: 操作类型（add/query/remove/list/stats）
- `--image-path`: 车辆图片路径
- `--vehicle-id`: 车辆唯一标识符
- `--metadata`: 车辆元数据（JSON格式）

## 使用示例

### 示例1: 车辆入库管理

```python
# 初始化工具
tool = VehicleRecognitionTool(
    config_file="config.yaml",
    model_weights="model.pth"
)

# 批量添加车辆
vehicles = [
    {"path": "car1.jpg", "id": "A001", "meta": {"plate": "京A12345"}},
    {"path": "car2.jpg", "id": "A002", "meta": {"plate": "京B67890"}},
    {"path": "car3.jpg", "id": "A003", "meta": {"plate": "沪C11111"}},
]

for vehicle in vehicles:
    tool.add_vehicle(vehicle["path"], vehicle["id"], vehicle["meta"])

print(f"数据库中共有 {len(tool.list_vehicles())} 辆车")
```

### 示例2: 车辆识别检查

```python
# 检查新车辆是否已记录
def check_vehicle(image_path):
    is_recorded, vehicle_id, similarity = tool.is_vehicle_recorded(image_path)
    
    if is_recorded:
        vehicle_info = tool.get_vehicle_info(vehicle_id)
        print(f"✅ 车辆已记录")
        print(f"   车辆ID: {vehicle_id}")
        print(f"   相似度: {similarity:.4f}")
        print(f"   车辆信息: {vehicle_info['metadata']}")
    else:
        print(f"❌ 车辆未记录，相似度: {similarity:.4f}")
        
        # 如果相似度较高但未超过阈值，可能需要人工确认
        if similarity > 0.5:
            print(f"⚠️  注意: 相似度较高，建议人工确认")

# 测试多张图片
test_images = ["test1.jpg", "test2.jpg", "test3.jpg"]
for img in test_images:
    print(f"\n检查图片: {img}")
    check_vehicle(img)
```

## 注意事项

1. **模型文件**: 确保配置文件和模型权重文件路径正确
2. **图片格式**: 支持常见图片格式（jpg, png, bmp等）
3. **相似度阈值**: 根据实际需求调整阈值，过低可能误判，过高可能漏检
4. **数据库备份**: 建议定期备份车辆数据库文件
5. **性能优化**: 大量车辆时建议使用并行处理模式

## 故障排除

### 常见问题

1. **导入错误**: 确保 FastReID 环境配置正确
2. **CUDA错误**: 检查GPU驱动和CUDA版本
3. **内存不足**: 减少batch size或使用CPU模式
4. **图片读取失败**: 检查图片文件是否损坏

### 日志查看

工具会输出详细的日志信息，可以通过日志排查问题：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 扩展功能

可以根据需要扩展以下功能：

- 批量导入/导出车辆数据
- 车辆特征可视化
- 相似车辆聚类分析
- Web API接口
- 实时视频流处理

## 许可证

本工具基于 FastReID 项目开发，遵循相应的开源许可证。
