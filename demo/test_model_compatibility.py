# encoding: utf-8
"""
测试模型和配置文件的兼容性
"""

import os
import sys
import torch
from pathlib import Path

sys.path.append('.')

from fastreid.config import get_cfg
from fastreid.utils.logger import setup_logger
from predictor import FeatureExtractionDemo

# 设置日志
setup_logger(name="fastreid")

def test_model_config_compatibility():
    """测试不同模型和配置的兼容性"""
    
    # 可能的配置和模型组合
    test_combinations = [
        {
            "name": "VERIWild配置 + VERIWild模型",
            "config": "configs/VERIWild/bagtricks_R50-ibn.yml",
            "weights": "veriwild_bot_R50-ibn.pth"
        },
        {
            "name": "VeRi配置 + VeRi模型", 
            "config": "logs/veri/sbs_R50-ibn/config.yaml",
            "weights": "logs/veri/sbs_R50-ibn/model_best.pth"
        }
    ]
    
    test_image = "datasets/veri/image_query/0002_c002_00030600_0.jpg"
    
    if not os.path.exists(test_image):
        print(f"测试图片不存在: {test_image}")
        return
    
    print("=== 模型配置兼容性测试 ===\n")
    
    for i, combo in enumerate(test_combinations, 1):
        print(f"{i}. {combo['name']}")
        print(f"   配置: {combo['config']}")
        print(f"   权重: {combo['weights']}")
        
        # 检查文件是否存在
        if not os.path.exists(combo['config']):
            print(f"   ❌ 配置文件不存在")
            continue
            
        if not os.path.exists(combo['weights']):
            print(f"   ❌ 权重文件不存在")
            continue
        
        try:
            # 测试模型加载
            cfg = get_cfg()
            cfg.merge_from_file(combo['config'])
            cfg.MODEL.WEIGHTS = combo['weights']
            cfg.MODEL.DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
            cfg.freeze()
            
            print(f"   📋 配置信息:")
            print(f"      输入尺寸: {cfg.INPUT.SIZE_TEST}")
            print(f"      骨干网络: {cfg.MODEL.BACKBONE.NAME}")
            print(f"      头部类型: {cfg.MODEL.HEADS.NAME}")
            print(f"      特征维度: {cfg.MODEL.BACKBONE.FEAT_DIM}")
            
            # 尝试初始化预测器
            predictor = FeatureExtractionDemo(cfg, parallel=False)
            print(f"   ✅ 模型加载成功")
            
            # 尝试特征提取
            import cv2
            image = cv2.imread(test_image)
            if image is not None:
                feature = predictor.run_on_image(image)
                print(f"   ✅ 特征提取成功，维度: {feature.shape}")
            else:
                print(f"   ⚠️  无法读取测试图片")
                
        except Exception as e:
            print(f"   ❌ 错误: {str(e)}")
        
        print()

def analyze_checkpoint_structure():
    """分析checkpoint的结构"""
    print("=== Checkpoint结构分析 ===\n")
    
    checkpoints = [
        "veriwild_bot_R50-ibn.pth",
        "logs/veri/sbs_R50-ibn/model_best.pth"
    ]
    
    for checkpoint_path in checkpoints:
        if not os.path.exists(checkpoint_path):
            print(f"❌ {checkpoint_path} 不存在")
            continue
            
        print(f"📁 {checkpoint_path}")
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location="cpu")
            
            print(f"   Checkpoint keys: {list(checkpoint.keys())}")
            
            if 'model' in checkpoint:
                model_state = checkpoint['model']
                print(f"   模型参数数量: {len(model_state)}")
                
                # 分析头部相关的参数
                head_keys = [k for k in model_state.keys() if 'head' in k.lower()]
                print(f"   头部相关参数: {head_keys}")
                
                # 分析分类器相关的参数
                classifier_keys = [k for k in model_state.keys() if 'classifier' in k.lower()]
                print(f"   分类器相关参数: {classifier_keys}")
                
                # 显示一些关键参数的形状
                key_params = ['heads.weight', 'heads.classifier.weight', 'heads.bottleneck.weight']
                for param in key_params:
                    if param in model_state:
                        print(f"   {param}: {model_state[param].shape}")
                
            else:
                print(f"   直接模型状态，参数数量: {len(checkpoint)}")
                
        except Exception as e:
            print(f"   ❌ 加载失败: {str(e)}")
        
        print()

def recommend_best_combination():
    """推荐最佳的配置组合"""
    print("=== 推荐配置 ===\n")
    
    # 检查VeRi数据集
    veri_exists = os.path.exists("datasets/veri")
    veriwild_config_exists = os.path.exists("configs/VERIWild/bagtricks_R50-ibn.yml")
    veriwild_model_exists = os.path.exists("veriwild_bot_R50-ibn.pth")
    veri_config_exists = os.path.exists("logs/veri/sbs_R50-ibn/config.yaml")
    veri_model_exists = os.path.exists("logs/veri/sbs_R50-ibn/model_best.pth")
    
    print("📊 资源检查:")
    print(f"   VeRi数据集: {'✅' if veri_exists else '❌'}")
    print(f"   VERIWild配置: {'✅' if veriwild_config_exists else '❌'}")
    print(f"   VERIWild模型: {'✅' if veriwild_model_exists else '❌'}")
    print(f"   VeRi配置: {'✅' if veri_config_exists else '❌'}")
    print(f"   VeRi模型: {'✅' if veri_model_exists else '❌'}")
    
    print(f"\n💡 推荐方案:")
    
    if veri_config_exists and veri_model_exists:
        print(f"   🥇 最佳选择: 使用VeRi配置 + VeRi模型")
        print(f"      - 配置和模型完全匹配")
        print(f"      - 专门针对VeRi数据集训练")
        print(f"      - 配置: logs/veri/sbs_R50-ibn/config.yaml")
        print(f"      - 模型: logs/veri/sbs_R50-ibn/model_best.pth")
    
    if veriwild_config_exists and veriwild_model_exists:
        print(f"   🥈 备选方案: 使用VERIWild配置 + VERIWild模型")
        print(f"      - VERIWild是VeRi的扩展数据集")
        print(f"      - 可能需要调整相似度阈值")
        print(f"      - 配置: configs/VERIWild/bagtricks_R50-ibn.yml")
        print(f"      - 模型: veriwild_bot_R50-ibn.pth")
    
    print(f"\n⚠️  注意事项:")
    print(f"   - 不要混用不同数据集的配置和模型")
    print(f"   - VERIWild模型的相似度分布可能与VeRi不同")
    print(f"   - 建议先用小样本测试相似度阈值")

def main():
    """主函数"""
    print("FastReID模型兼容性分析工具")
    print("=" * 50)
    
    try:
        # 1. 分析checkpoint结构
        analyze_checkpoint_structure()
        
        # 2. 测试模型配置兼容性
        test_model_config_compatibility()
        
        # 3. 推荐最佳配置
        recommend_best_combination()
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
